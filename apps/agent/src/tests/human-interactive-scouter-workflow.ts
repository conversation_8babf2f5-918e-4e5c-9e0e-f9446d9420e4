import { searchCreatorsWorkflow } from '@/workflows/searchCreatorsWorkflow';
import { <PERSON><PERSON> } from '@mastra/core';
import { input } from '@inquirer/prompts';
import { creatorHashtagScout } from '@/agents/creatorHashtagScout';

// Register the workflow
const mastra = new Mastra({
  agents: { creatorHashtagScout },
  workflows: { searchCreatorsWorkflow },
});

async function runKOLScouterDemo() {
  const registeredWorkflow = mastra.getWorkflow('searchCreatorsWorkflow');
  const run = registeredWorkflow.createRun();

  // Start the workflow with content that needs review
  console.log('Starting kol scouter workflow...');

  run.watch(async (watchEvents) => {
    const steps = Object.entries(watchEvents.payload.workflowState.steps);

    for (const [stepId, step] of steps) {
      // console.log('path', step);
      const path = step;

      if (path && path.status === 'suspended') {
        // console.log(JSON.stringify(path, null, 2));
        // console.log('Payload', path.payload);
        // console.log('Suspended path:', id);
        const { message, messages } = path.payload!;
        console.log('--->', stepId);
        console.log('\n===================================');
        console.log(message.text);
        console.log('===================================\n');

        const answer = await input({
          message: 'Please enter your response:',
        });

        console.log('user responded:', answer);

        await run.resume({
          step: stepId,
          resumeData: {
            messages: messages,
            userInputMessage: answer,
          },
        });

        console.log('resumed');
      }
    }
  });

  await run.start({
    inputData: {
      targetCreatorDescription:
        'I want to find kols who can promote MLBB in Japan',
    },
  });

  // let isStepSuspended =
  //   result.activePaths.get('analyzeRequirement')?.status === 'suspended';

  // while (isStepSuspended) {
  //   const { message, messages } =
  //     result.activePaths.get('analyzeRequirement')?.suspendPayload;

  //   console.log('\n===================================');
  //   console.log(message);
  //   console.log('===================================\n');

  //   const answer = await input({
  //     message: 'Please enter your response:',
  //   });

  //   await run.resume({
  //     stepId: 'analyzeRequirement',
  //     context: {
  //       messages: messages,
  //       userInputMessage: answer,
  //     },
  //   });

  //   isStepSuspended =
  //     result.activePaths.get('analyzeRequirement')?.status === 'suspended';
  // }

  // console.log('Final output:', result.results);
}

runKOLScouterDemo().catch(console.error);
