import { <PERSON><PERSON> } from '@mastra/core';
import { createLogger } from '@mastra/core/logger';
import { PostgresStore } from '@mastra/pg';
import { env } from '@/lib/env';
import { aiScriptWorkflow } from '@/workflows/aiScriptWorkflow';
import { campaignAnalyzer } from '@/agents/campaignAnalyzerAgent';
import { WorkflowNames } from '@repo/constants';
import { creatorHashtagScout } from '@/agents/creatorHashtagScout';
import { searchCreatorsWorkflow } from '@/workflows/searchCreatorsWorkflow';

export const storage = new PostgresStore({
  connectionString: env.DB_POSTGRES_URL,
});

export const mastra = new Mastra({
  agents: {
    campaignAnalyzer,
    creatorHashtagScout,
  },
  workflows: {
    // [WorkflowNames.aiScriptWorkflow]: aiScriptWorkflow,
    [WorkflowNames.searchCreatorsWorkflow]: searchCreatorsWorkflow,
  },
  logger: createLogger({
    name: 'J<PERSON>_DEV',
    level: 'info',
  }),
  vectors: {},
  storage: storage,
});
