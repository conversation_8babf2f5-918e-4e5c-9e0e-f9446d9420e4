import { extract<PERSON><PERSON><PERSON><PERSON><PERSON> } from '@/lib/utils';
import { createStep, createWorkflow } from '@mastra/core/workflows';
import { CoreMessage, TextPart } from 'ai';
import { z } from 'zod';
// import { workflowDbService } from '@/services/index';

// Schema for the hashtag search results
const searchHashtagSchema = z.object({
  core: z.array(z.string()),
  adjacent: z.array(z.string()),
  rationale: z.string().optional(),
});

// Schema for the video scraping results (placeholder)
const videoScrapingSchema = z.object({
  scrapedVideos: z.number(),
  hashtags: z.array(z.string()),
});

const workflowOutputSchema = z.object({
  desiredCreators: z.number(),
  scoutedCreators: z.number(),
  results: z.array(
    z.object({
      url: z.string().url(),
      reason: z.string(),
    }),
  ),
});

export const searchCreatorsWorkflow = createWorkflow({
  id: 'search-creator',
  inputSchema: z.object({
    targetCreatorDescription: z.string(),
  }),
  outputSchema: workflowOutputSchema,
});

// Step 1: Analyze the user's requirements and generate hashtags
const analyzeRequirementStep = createStep({
  id: 'analyzeRequirement',
  inputSchema: z.object({
    targetCreatorDescription: z.string(),
  }),
  resumeSchema: z.object({
    userInputMessage: z.string().optional(),
    messages: z.array(z.any()),
  }),
  outputSchema: searchHashtagSchema,
  execute: async ({ inputData, resumeData, suspend, mastra }) => {
    const description = inputData.targetCreatorDescription;
    // console.log("Analyzing user's requirements:", description);

    console.log('inputData', inputData);
    console.log('resumeData', resumeData);

    const scouter = mastra?.getAgent('creatorHashtagScout');
    if (!scouter) {
      throw new Error('Campaign analyzer agent not found');
    }

    // Try to retrieve cached messages from context
    let messages: CoreMessage[] = [];
    const cachedContext = resumeData && resumeData.messages;

    if (cachedContext) {
      // If we have cached messages, use them
      console.log('Found cached messages, resuming conversation');
      messages = cachedContext as CoreMessage[];

      // Add the user's response from the resumed workflow
      if (resumeData.userInputMessage) {
        const userResponse: CoreMessage = {
          role: 'user',
          content: resumeData.userInputMessage,
        };
        messages.push(userResponse);

        // Save updated messages to context after adding user response
        // await workflowDbService.saveWorkflowContext(
        //   runId,
        //   'analyze_requirement_messages',
        //   { messages },
        // );
      }
    } else {
      // If no cached messages, start a new conversation
      console.log('No cached messages found, starting new conversation');
      const userDescription: CoreMessage = {
        role: 'user',
        content: description,
      };
      messages.push(userDescription);
    }

    // Generate a response from the agent
    const resp = await scouter.generate(messages);
    const assistantMessage = resp.response.messages[0];
    const content = (assistantMessage.content as Array<TextPart>)[0];
    // console.log('content', content);
    let parsedResult;
    // Check if the response is in the expected format
    try {
      // Extract the first JSON object from the response
      parsedResult = extractFirstJson(content.text);
      console.log('parsedResult', parsedResult);
      // parsedResult = JSON.parse(content.text);
    } catch (e) {}

    const parseResult = searchHashtagSchema.safeParse(parsedResult);
    if (parseResult.success) {
      console.log('parseResult.data', parseResult.data);
      return parseResult.data;
    } else {
      // console.log('parseResult.error', parseResult.error);
    }

    // If not in expected format, add to messages and suspend
    messages.push(assistantMessage);

    // TODO: Save messages to context before suspending (looks liek we don't need this anymore)
    // resumeData!.messages = messages;

    // Suspend and wait for user input
    // This will pause execution and when resumed, the step will restart from the beginning
    // with the context preserved in the database
    await suspend({
      messages,
      message: content,
    });

    // This code will only execute if suspend() resolves without restarting the step
    // (which shouldn't happen in normal operation)
    // We still need to return a valid result to satisfy TypeScript
    return {
      core: [],
      adjacent: [],
    };
  },
});

// Step 2: Scrape videos based on hashtags (placeholder)
const scrapeVideosStep = createStep({
  id: 'scrape-videos',
  inputSchema: searchHashtagSchema,
  outputSchema: videoScrapingSchema,
  execute: async ({ inputData }) => {
    const { core, adjacent } = inputData;
    console.log('Scraping videos for hashtags:', { core, adjacent });

    // This is a placeholder for the actual video scraping logic
    // In a real implementation, you would call your video scraping service here

    return {
      scrapedVideos: Math.floor(Math.random() * 100) + 1, // Placeholder: random number of videos
      hashtags: [...core, ...adjacent],
    };
  },
});

// Define the workflow steps and their relationships
searchCreatorsWorkflow
  .then(analyzeRequirementStep)
  .then(scrapeVideosStep)
  .commit();
