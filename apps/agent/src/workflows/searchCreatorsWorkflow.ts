import { extract<PERSON><PERSON><PERSON><PERSON><PERSON> } from '@/lib/utils';
import { createStep, createWorkflow } from '@mastra/core/workflows';
import { CoreMessage, TextPart } from 'ai';
import { z } from 'zod';
// import { workflowDbService } from '@/services/index';

// Schema for the hashtag search results
const searchHashtagSchema = z.object({
  core: z.array(z.string()),
  adjacent: z.array(z.string()),
  rationale: z.string().optional(),
});

// Schema for the video scraping results (placeholder)
const videoScrapingSchema = z.object({
  scrapedVideos: z.number(),
  hashtags: z.array(z.string()),
});

const workflowOutputSchema = z.object({
  desiredCreators: z.number(),
  scoutedCreators: z.number(),
  results: z.array(
    z.object({
      url: z.string().url(),
      reason: z.string(),
    }),
  ),
});

export const searchCreatorsWorkflow = createWorkflow({
  id: 'search-creator',
  inputSchema: z.object({
    targetCreatorDescription: z.string(),
  }),
  outputSchema: workflowOutputSchema,
});

// Step 1: Analyze the user's requirements and generate hashtags
const analyzeRequirementStep = createStep({
  id: 'analyzeRequirement',
  inputSchema: z.object({
    targetCreatorDescription: z.string(),
  }),
  resumeSchema: z.object({
    userInputMessage: z.string().optional(),
    messages: z.array(z.any()),
  }),
  outputSchema: searchHashtagSchema,
  execute: async ({ inputData, resumeData, suspend, mastra }) => {
    console.log('inputData', inputData);
    console.log('resumeData', resumeData);

    const scouter = mastra?.getAgent('creatorHashtagScout');
    if (!scouter) {
      throw new Error('Campaign analyzer agent not found');
    }

    // Initialize messages array
    let messages: CoreMessage[] = [];

    // Check if this is a resume (either resumeData exists OR inputData has been contaminated with resume data)
    const isResume = resumeData?.messages || (inputData as any).messages;

    if (isResume) {
      // If we have cached messages, use them (this is a resume)
      console.log('Found cached messages, resuming conversation');

      // Get messages from resumeData first, fallback to inputData if framework merged them
      const cachedMessages =
        resumeData?.messages || (inputData as any).messages;
      messages = [...cachedMessages] as CoreMessage[]; // Create a copy to avoid mutation

      // Get user input message from resumeData first, fallback to inputData
      const userInputMessage =
        resumeData?.userInputMessage || (inputData as any).userInputMessage;

      // Add the user's response from the resumed workflow
      if (userInputMessage) {
        console.log('user responded:', userInputMessage);
        const userResponse: CoreMessage = {
          role: 'user',
          content: userInputMessage,
        };
        messages.push(userResponse);
      }
    } else {
      // If no cached messages, start a new conversation
      console.log('No cached messages found, starting new conversation');
      const description = inputData.targetCreatorDescription;
      const userDescription: CoreMessage = {
        role: 'user',
        content: description,
      };
      messages.push(userDescription);
    }

    // Generate a response from the agent
    const resp = await scouter.generate(messages);
    const assistantMessage = resp.response.messages[0];
    const content = (assistantMessage.content as Array<TextPart>)[0];

    let parsedResult;
    // Check if the response is in the expected format
    try {
      // Extract the first JSON object from the response
      parsedResult = extractFirstJson(content.text);
      console.log('parsedResult', parsedResult);
    } catch (e) {
      // JSON parsing failed, continue to suspend
    }

    const parseResult = searchHashtagSchema.safeParse(parsedResult);
    if (parseResult.success) {
      console.log('parseResult.data', parseResult.data);
      return parseResult.data;
    }

    // If not in expected format, add assistant message to conversation and suspend
    const updatedMessages = [...messages, assistantMessage];

    console.log('before suspend, we are here');

    // Suspend and wait for user input
    // When resumed, the step will restart with the updated messages in resumeData
    await suspend({
      messages: updatedMessages,
      message: content,
    });

    console.log('after suspend, we are here');

    // This code should not execute in normal operation since suspend restarts the step
    return {
      core: [],
      adjacent: [],
    };
  },
});

// Step 2: Scrape videos based on hashtags (placeholder)
const scrapeVideosStep = createStep({
  id: 'scrape-videos',
  inputSchema: searchHashtagSchema,
  outputSchema: videoScrapingSchema,
  execute: async ({ inputData }) => {
    const { core, adjacent } = inputData;
    console.log('Scraping videos for hashtags:', { core, adjacent });

    // This is a placeholder for the actual video scraping logic
    // In a real implementation, you would call your video scraping service here

    return {
      scrapedVideos: Math.floor(Math.random() * 100) + 1, // Placeholder: random number of videos
      hashtags: [...core, ...adjacent],
    };
  },
});

// Define the workflow steps and their relationships
searchCreatorsWorkflow
  .then(analyzeRequirementStep)
  .then(scrapeVideosStep)
  .commit();
