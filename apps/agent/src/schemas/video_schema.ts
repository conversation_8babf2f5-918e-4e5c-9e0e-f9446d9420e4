export interface SocialVideoSchema {
  title: string;
  hashtags: string[];
  description: string;
  platform: string;
  video_id: string;
  video_url: string;
  thumbnail_url: string;
  publish_time: string;
  duration: number;
  view_count: number;
  like_count: number;
  comment_count: number;
  share_count: number;
}

export interface TiktokVideoAuthorSchema {
  nickname: string;
  unique_id: string;
  sec_uid: string;
  region: string;
  language: string;
  signature: string;
  follower_count: number;
  avatar_url: string;
}

export interface TiktokVideoSchema extends SocialVideoSchema {
  author: TiktokVideoAuthorSchema;
}

export interface TiktokChallengeSchema {
  challenge_id: string;
  challenge_name: string;
  use_count: number;
  user_count: number;
  view_count: number;
}
